name: Go

on:
  push:
    branches: [v5]
  pull_request:
    branches: [v5]

jobs:
  build:
    name: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go-version: [1.21.x]

    steps:
      - name: Set up ${{ matrix.go-version }}
        uses: actions/setup-go@v2
        with:
          go-version: ${{ matrix.go-version }}

      - name: Checkout code
        uses: actions/checkout@v2

      - name: Test
        run: make test
